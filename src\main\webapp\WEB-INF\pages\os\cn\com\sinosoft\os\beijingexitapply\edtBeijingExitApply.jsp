<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>出京申请 - 维护</title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<%@ include file="/common/taglibs.jsp"%>
	<script type="text/javascript">
		var form;
		$(document).ready(function() {
			mini.parse();
			form = new mini.Form("form1");
		});
		//保存
		function save(e) {
			form.validate();
			if (form.isValid() == false) {
				//提示验证错误信息，400为提示框宽度，300为提示框高度
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}
			
			if ("${pageState}" == "add") {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
			} else {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
			}
			document.form1.submit();
			waitClick();
		}
	</script>
</head>
<body>
	<div class="mini-fit">
	<iframe name="post_frame" id="post_frame" style="display: none;"></iframe>
	<form id="form1" name="form1" method="post" target="post_frame"
		enctype="multipart/form-data">
		<s:token></s:token>
		<div style="display: none;">
			<bspHtml:Hidden property="result.id"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataSource"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataModyTime"></bspHtml:Hidden>
		</div>
	<center>
	<table class="tab-1" cellpadding="5" cellspacing="0" border="1" align="center">
		<COLGROUP>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
		</COLGROUP>
		<tr>
			<td align="right" class="bgcolor">
				申请人姓名：
			</td>
			<td>
				<bspHtml:TextArea property="result.applyName"
					style="width:100%;height:100px;"
					emptyText="请输入申请人姓名" maxLength="4000"
					vtype="rangeChar:0,4000;"
					rangeCharErrorText="[申请人姓名] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextArea>
			</td>
			<td align="right" class="bgcolor">
				身份类型：
			</td>
			<td>
				<bspHtml:TextBox property="result.identityType"
					style="width:100%;"
					emptyText="请输入身份类型" maxLength="50"
					vtype="rangeChar:0,50;"
					rangeCharErrorText="[身份类型] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				科室：
			</td>
			<td>
				<bspHtml:TextBox property="result.department"
					style="width:100%;"
					emptyText="请输入科室" maxLength="100"
					vtype="rangeChar:0,100;"
					rangeCharErrorText="[科室] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				申请日期：
			</td>
			<td>
				<bspHtml:DatePicker property="result.applyDate"
					style="width:100%;"
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="请选择申请日期">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				开始时间：
			</td>
			<td>
				<bspHtml:DatePicker property="result.startDate"
					style="width:100%;"
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="请选择开始时间">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				结束时间：
			</td>
			<td>
				<bspHtml:DatePicker property="result.endDate"
					style="width:100%;"
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="请选择结束时间">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				出京天数：
			</td>
			<td>
				<bspHtml:DatePicker property="result.travelDays"
					style="width:100%;"
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="请选择出京天数">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				目的地：
			</td>
			<td>
				<bspHtml:TextBox property="result.applyDestn"
					style="width:100%;"
					emptyText="请输入目的地" maxLength="100"
					vtype="rangeChar:0,100;"
					rangeCharErrorText="[目的地] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				出京事由：
			</td>
			<td>
				<bspHtml:TextArea property="result.travelReason"
					style="width:100%;height:100px;"
					emptyText="请输入出京事由" maxLength="500"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[出京事由] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextArea>
			</td>
			<td align="right" class="bgcolor">
				流程标识：
			</td>
			<td>
				<bspHtml:TextBox property="result.piId"
					style="width:100%;"
					emptyText="请输入流程标识" maxLength="50"
					vtype="rangeChar:0,50;"
					rangeCharErrorText="[流程标识] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				审核状态1：
			</td>
			<td>
				<bspHtml:TextBox property="result.auditState"
					style="width:100%;"
					emptyText="请输入审核状态1" maxLength="10"
					vtype="rangeChar:0,10;"
					rangeCharErrorText="[审核状态1] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;是否有效：
			</td>
			<td colspan="1">
				<bspHtml:RadioButtonList property="result.state"
					style="width:100%;" value="1" required="true" requiredErrorText="[是否有效] 不能为空"
					url="PUB_STATE"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
		</tr>
	</table>
	<table class="tab-1" cellpadding="5" cellspacing="0" border="1" align="center" style="margin-top: 10px;">
		<COLGROUP>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
		</COLGROUP>
		<c:if test="${pageState ne 'add' }">
		<tr>
			<td align="right" class="bgcolor">
				添加地区：
			</td>
			<td>
				<bspHtml:ZoneCode property="result.addZone"
					editState="view" addState="view"
					org="result.addOrg">
				</bspHtml:ZoneCode>
			</td>
			<td align="right" class="bgcolor">
				添加机构：
			</td>
			<td>
				<bspHtml:OrgCode property="result.addOrg"
					showNullItem="true" nullItemText="请选择"
					editState="view" addState="view"
					department="result.addDep" showUser="true">
				</bspHtml:OrgCode>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				添加科室：
			</td>
			<td>
				<bspHtml:Department property="result.addDep"
					showClose="true" oncloseclick="util_treeSelect_onCloseClick"
					editState="view" addState="view"
					showUser="true" multiSelect="true">
				</bspHtml:Department>
			</td>
			<td align="right" class="bgcolor">
				添加人：
			</td>
			<td>
				<bspHtml:ComboBox property="result.addUser"
					showNullItem="true" nullItemText="请选择"
					url="DD_PERM_USERS_BY_USERNAME&param=${result.addUser }"
					textField="NAME" valueField="ID"
					editState="view" addState="view">
				</bspHtml:ComboBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				添加时间：
			</td>
			<td>
				<bspHtml:DatePicker property="result.addTime" format="yyyy-MM-dd HH:mm:ss"
					editState="view" addState="view">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				修改地区：
			</td>
			<td>
				<bspHtml:ZoneCode property="result.modyZone"
					editState="view" addState="view"
					org="result.modyOrg">
				</bspHtml:ZoneCode>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				修改机构：
			</td>
			<td>
				<bspHtml:OrgCode property="result.modyOrg"
					showNullItem="true" nullItemText="请选择"
					editState="view" addState="view"
					department="result.modyDep" showUser="true">
				</bspHtml:OrgCode>
			</td>
			<td align="right" class="bgcolor">
				修改科室：
			</td>
			<td>
				<bspHtml:Department property="result.modyDep"
					showClose="true" oncloseclick="util_treeSelect_onCloseClick"
					editState="view" addState="view"
					showUser="true" multiSelect="true">
				</bspHtml:Department>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				修改人：
			</td>
			<td>
				<bspHtml:ComboBox property="result.modyUser"
					showNullItem="true" nullItemText="请选择"
					url="DD_PERM_USERS_BY_USERNAME&param=${result.modyUser }"
					textField="NAME" valueField="ID"
					editState="view" addState="view">
				</bspHtml:ComboBox>
			</td>
			<td align="right" class="bgcolor">
				修改时间：
			</td>
			<td colspan="1">
				<bspHtml:DatePicker property="result.modyTime" format="yyyy-MM-dd HH:mm:ss"
					editState="view" addState="view">
				</bspHtml:DatePicker>
			</td>
		</tr>
		</c:if>
	</table>
	</center>
	</form>
	</div>
	<div class="mini-toolbar"
		style="text-align: center; padding: 6px; border: 0;">
		<c:if test="${pageState ne 'view'}">
			<a class="mini-button mini-button-iconTop" iconCls="icon-save"
				onClick="save" style="margin-right: 20px;">&nbsp;&nbsp;保存&nbsp;&nbsp;</a>
		</c:if>
		<a class="mini-button mini-button-iconTop" iconCls="icon-close"
			onClick="CloseWindow('cancel')">&nbsp;&nbsp;关闭&nbsp;&nbsp;</a>
	</div>
</body>
</html>